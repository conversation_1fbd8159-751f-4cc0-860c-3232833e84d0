package evm

import (
	"byd_wallet/common/constant"
	"byd_wallet/utils"
	"context"
	"math/big"

	"github.com/ethereum/go-ethereum"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
)

// gas.go - EVM paymaster gas费用计算相关功能
// 包含gas费用估算、默认费用计算等功能
//
// 重要更新说明：
// 本文件中的gas费用估算逻辑已更新为使用传统交易格式，原因如下：
// 1. 兼容性优先：确保gas转账在所有EVM链上都能稳定工作
// 2. 一致性保证：与实际发送的gas转账交易格式保持一致
// 3. 简化逻辑：避免EIP-1559复杂的费用计算可能带来的估算误差

// estimateGasTransferFee 估算gas转账交易的费用
//
// 估算策略说明：
// 1. 格式一致性：使用传统交易格式进行估算，确保与实际交易格式一致
// 2. 动态估算：通过eth_estimateGas获取准确的gas limit需求
// 3. 安全缓冲：应用链特定的安全系数，防止gas不足导致交易失败
// 4. 多重回退：网络调用失败时有完善的回退机制
//
// 参数:
//   - ctx: 上下文对象
//   - userTx: 用户的原始交易，用于获取发送者地址和gas价格参考
//
// 返回值:
//   - *big.Int: 估算的gas转账费用（以wei为单位）
//   - error: 错误信息
func (pm *Paymaster) estimateGasTransferFee(ctx context.Context, userTx *types.Transaction) (*big.Int, error) {
	pm.log.Debugf("开始估算%s链gas转账交易费用", pm.chainName)

	// 步骤1: 获取交易发送者地址
	senderAddr, err := utils.GetTxSender(userTx)
	if err != nil {
		pm.log.Warnf("获取交易发送者地址失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤2: 获取EVM客户端（关键步骤，失败时立即回退）
	client, err := pm.evmCli.Select(pm.chainIndex)
	if err != nil {
		pm.log.Warnf("无法获取%s链EVM客户端（可能在测试环境），回退到默认gas转账费用计算: %v", pm.chainName, err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤3: 获取热钱包私钥和地址
	hotWalletPrivateKey, err := pm.hotAccountReader.GetHotAccount(ctx, pm.chainIndex)
	if err != nil {
		pm.log.Warnf("获取热钱包私钥失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	hotWalletAddr, _, err := utils.GetAddressByPrivateKey(hotWalletPrivateKey)
	if err != nil {
		pm.log.Warnf("解析热钱包地址失败，使用默认gas转账费用: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤4: 获取当前gas价格
	// 为了与gas转账交易的传统格式保持一致，这里也使用传统的gas价格获取方式
	suggestedGasPrice, err := client.SuggestGasPrice(ctx)
	if err != nil {
		pm.log.Warnf("获取建议gas价格失败，使用用户交易的gas价格: %v", err)
		suggestedGasPrice = userTx.GasPrice()
		if suggestedGasPrice == nil || suggestedGasPrice.Sign() <= 0 {
			pm.log.Warnf("用户交易gas价格无效，回退到默认gas转账费用计算")
			return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
		}
	}

	// 应用1.2x倍数策略以确保gas转账交易能够快速确认
	gasPriceMultiplier := decimal.NewFromFloat(1.2)
	adjustedGasPrice := decimal.NewFromBigInt(suggestedGasPrice, 0).Mul(gasPriceMultiplier).BigInt()

	// 步骤5: 创建模拟的gas转账交易用于估算
	// 使用较小的转账金额进行估算（1 wei），但保持与实际交易相同的格式
	simulationValue := big.NewInt(1)

	// 步骤6: 使用EstimateGas进行精确估算
	// 使用传统交易格式进行估算，确保与实际发送的交易格式一致
	estimatedGasLimit, err := client.EstimateGas(ctx, ethereum.CallMsg{
		From:     hotWalletAddr,
		To:       &senderAddr,
		GasPrice: adjustedGasPrice, // 使用传统gas价格字段而非EIP-1559字段
		Value:    simulationValue,
		Data:     nil,
	})
	if err != nil {
		pm.log.Warnf("动态gas估算失败，回退到默认gas转账费用计算: %v", err)
		return pm.getDefaultGasTransferFee(userTx.GasPrice()), nil
	}

	// 步骤7: 应用安全系数
	// 使用链特定的gas倍数，不同链可能需要不同的安全系数
	safetyFactor := pm.getChainSpecificGasMultiplier()
	safeGasLimit := decimal.NewFromInt(int64(estimatedGasLimit)).Mul(safetyFactor)
	finalGasLimit := safeGasLimit.BigInt()

	// 步骤8: 计算总gas费用
	// 使用调整后的gas价格计算最终费用
	totalGasTransferFee := new(big.Int).Mul(finalGasLimit, adjustedGasPrice)

	pm.log.Debugf("动态gas转账费用估算成功 - 估算GasLimit: %d, 安全GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		estimatedGasLimit, finalGasLimit.String(), adjustedGasPrice.String(), totalGasTransferFee.String())

	return totalGasTransferFee, nil
}

// getDefaultGasTransferFee 获取默认的gas转账费用（作为后备方案）
// 参数:
//   - userGasPrice: 用户交易的gas价格，用作参考
//
// 返回值:
//   - *big.Int: 默认的gas转账费用（以wei为单位）
func (pm *Paymaster) getDefaultGasTransferFee(userGasPrice *big.Int) *big.Int {
	// 使用标准ETH转账的gas limit (21000) 乘以安全系数 (1.2)
	defaultGasLimit := big.NewInt(21000)
	safetyFactor := decimal.NewFromFloat(1.2)
	safeGasLimit := decimal.NewFromBigInt(defaultGasLimit, 0).Mul(safetyFactor)

	// 使用用户交易的gas价格或默认值
	gasPrice := userGasPrice
	if gasPrice == nil || gasPrice.Sign() <= 0 {
		gasPrice = big.NewInt(constant.DefaultBaseFee + constant.DefaultGasTipCap)
	}

	// 计算默认gas转账费用
	defaultFee := new(big.Int).Mul(safeGasLimit.BigInt(), gasPrice)

	pm.log.Debugf("使用默认gas转账费用 - GasLimit: %s, GasPrice: %s wei, 总费用: %s wei",
		safeGasLimit.String(), gasPrice.String(), defaultFee.String())

	return defaultFee
}

// getBaseUnit 获取链的基本单位
// 返回值:
//   - decimal.Decimal: 链的基本单位
func (pm *Paymaster) getBaseUnit() decimal.Decimal {
	switch pm.chainIndex {
	case constant.EthChainIndex, constant.ArbChainIndex, constant.OptimismChainIndex, constant.BaseChainIndex:
		return constant.BaseUnitPerETH
	case constant.BscChainIndex:
		return constant.BaseUnitPerBNB // BSC链BNB使用18位精度，与ETH相同
	case constant.PolChainIndex:
		return constant.BaseUnitPerETH // Polygon也使用18位精度
	default:
		return constant.BaseUnitPerETH // 默认使用ETH单位
	}
}

// getChainSpecificGasMultiplier 获取链特定的gas费用倍数
// 不同链可能需要不同的安全系数
func (pm *Paymaster) getChainSpecificGasMultiplier() decimal.Decimal {
	switch pm.chainIndex {
	case constant.OptimismChainIndex:
		// Optimism由于L1 data fee的存在，需要稍高的安全系数
		return decimal.NewFromFloat(1.3)
	case constant.BscChainIndex:
		// BSC链使用标准系数，与原BSC paymaster保持一致
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.ArbChainIndex:
		// Arbitrum通常gas费用较低，使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.BaseChainIndex:
		// Base链使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.EthChainIndex:
		// 以太坊主网使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	case constant.PolChainIndex:
		// Polygon使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	default:
		// 默认使用标准系数
		return decimal.NewFromFloat(DefaultGasLimitMultiplier)
	}
}
