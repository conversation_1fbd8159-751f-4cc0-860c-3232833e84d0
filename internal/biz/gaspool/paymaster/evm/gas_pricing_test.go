package evm

import (
	"byd_wallet/common/constant"
	"context"
	"math/big"
	"testing"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/core/types"
	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// TestGasPricingStrategy 测试1.2x gas价格策略
func TestGasPricingStrategy(t *testing.T) {
	tests := []struct {
		name               string
		chainIndex         int64
		suggestedTip       *big.Int
		expectedMultiplier float64
	}{
		{
			name:               "Ethereum链1.2x策略",
			chainIndex:         constant.EthChainIndex,
			suggestedTip:       big.NewInt(**********), // 2 Gwei
			expectedMultiplier: 1.2,
		},
		{
			name:               "Polygon链1.2x策略",
			chainIndex:         constant.PolChainIndex,
			suggestedTip:       big.NewInt(30000000000), // 30 Gwei
			expectedMultiplier: 1.2,
		},
		{
			name:               "Arbitrum链1.2x策略",
			chainIndex:         constant.ArbChainIndex,
			suggestedTip:       big.NewInt(100000000), // 0.1 Gwei
			expectedMultiplier: 1.2,
		},
		{
			name:               "Base链1.2x策略",
			chainIndex:         constant.BaseChainIndex,
			suggestedTip:       big.NewInt(1000000000), // 1 Gwei
			expectedMultiplier: 1.2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建测试paymaster
			pm := createTestPaymaster(tt.chainIndex)

			// 模拟EVM客户端
			mockClient := NewMockEvmClient(t)
			pm.evmCli = &MockMultiChainClient{client: mockClient}

			// 设置mock期望
			mockClient.On("SuggestGasTipCap", mock.Anything).Return(tt.suggestedTip, nil)
			mockClient.On("EstimateGas", mock.Anything, mock.Anything).Return(uint64(21000), nil)
			mockClient.On("ChainID", mock.Anything).Return(big.NewInt(1), nil)
			mockClient.On("PendingNonceAt", mock.Anything, mock.Anything).Return(uint64(0), nil)
			mockClient.On("SendTransaction", mock.Anything, mock.Anything).Return(nil)

			// 模拟热钱包读取器
			mockHotAccountReader := NewMockHotAccountReader(t)
			pm.hotAccountReader = mockHotAccountReader
			mockHotAccountReader.On("GetHotAccount", mock.Anything, tt.chainIndex).Return("0x1234567890123456789012345678901234567890123456789012345678901234", nil)

			ctx := context.Background()
			senderAddr := common.HexToAddress("******************************************")
			gasAmount := decimal.NewFromInt(1000000000000000000) // 1 ETH

			// 执行转账
			txHash, err := pm.transferGasToSender(ctx, senderAddr, gasAmount)

			// 验证结果
			assert.NoError(t, err)
			assert.NotEmpty(t, txHash)

			// 验证mock调用
			mockClient.AssertExpectations(t)
			mockHotAccountReader.AssertExpectations(t)
		})
	}
}

// TestGasLimitEstimation 测试gas limit估算功能
func TestGasLimitEstimation(t *testing.T) {
	tests := []struct {
		name               string
		chainIndex         int64
		estimatedGasLimit  uint64
		expectedMultiplier float64
	}{
		{
			name:               "Ethereum标准转账",
			chainIndex:         constant.EthChainIndex,
			estimatedGasLimit:  21000,
			expectedMultiplier: 1.2,
		},
		{
			name:               "Optimism L2转账",
			chainIndex:         constant.OptimismChainIndex,
			estimatedGasLimit:  21000,
			expectedMultiplier: 1.3, // Optimism使用更高的安全系数
		},
		{
			name:               "复杂合约交互",
			chainIndex:         constant.EthChainIndex,
			estimatedGasLimit:  150000,
			expectedMultiplier: 1.2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pm := createTestPaymaster(tt.chainIndex)

			// 测试链特定的gas倍数
			multiplier := pm.getChainSpecificGasMultiplier()
			expectedDecimal := decimal.NewFromFloat(tt.expectedMultiplier)

			assert.True(t, multiplier.Equal(expectedDecimal),
				"链 %d 的gas倍数应该是 %f，实际是 %s",
				tt.chainIndex, tt.expectedMultiplier, multiplier.String())

			// 计算最终gas limit
			finalGasLimit := decimal.NewFromInt(int64(tt.estimatedGasLimit)).Mul(multiplier)
			expectedFinalLimit := decimal.NewFromInt(int64(tt.estimatedGasLimit)).Mul(expectedDecimal)

			assert.True(t, finalGasLimit.Equal(expectedFinalLimit),
				"最终gas limit计算错误，期望 %s，实际 %s",
				expectedFinalLimit.String(), finalGasLimit.String())
		})
	}
}

// TestGasPriceCalculation 测试gas价格计算逻辑
func TestGasPriceCalculation(t *testing.T) {
	baseTip := big.NewInt(**********)  // 2 Gwei
	baseFee := big.NewInt(10000000000) // 10 Gwei
	multiplier := decimal.NewFromFloat(1.2)

	// 计算调整后的价格
	adjustedTip := decimal.NewFromBigInt(baseTip, 0).Mul(multiplier).BigInt()
	adjustedBaseFee := decimal.NewFromBigInt(baseFee, 0).Mul(multiplier).BigInt()
	gasFeeCap := new(big.Int).Add(adjustedTip, adjustedBaseFee)

	// 验证计算结果
	expectedTip := big.NewInt(2400000000)      // 2.4 Gwei
	expectedBaseFee := big.NewInt(1**********) // 12 Gwei
	expectedFeeCap := big.NewInt(14400000000)  // 14.4 Gwei

	assert.Equal(t, expectedTip, adjustedTip, "调整后的tip价格计算错误")
	assert.Equal(t, expectedBaseFee, adjustedBaseFee, "调整后的base fee计算错误")
	assert.Equal(t, expectedFeeCap, gasFeeCap, "最终gas fee cap计算错误")

	t.Logf("原始tip: %s wei, 调整后tip: %s wei", baseTip.String(), adjustedTip.String())
	t.Logf("原始baseFee: %s wei, 调整后baseFee: %s wei", baseFee.String(), adjustedBaseFee.String())
	t.Logf("最终gasFeeCap: %s wei", gasFeeCap.String())
}

// TestErrorHandling 测试错误处理场景
func TestErrorHandling(t *testing.T) {
	pm := createTestPaymaster(constant.EthChainIndex)

	// 模拟EVM客户端
	mockClient := NewMockEvmClient(t)
	pm.evmCli = &MockMultiChainClient{client: mockClient}

	// 模拟EstimateGas失败的情况
	mockClient.On("SuggestGasTipCap", mock.Anything).Return(big.NewInt(**********), nil)
	mockClient.On("EstimateGas", mock.Anything, mock.Anything).Return(uint64(0), assert.AnError)
	mockClient.On("ChainID", mock.Anything).Return(big.NewInt(1), nil)
	mockClient.On("PendingNonceAt", mock.Anything, mock.Anything).Return(uint64(0), nil)
	mockClient.On("SendTransaction", mock.Anything, mock.Anything).Return(nil)

	// 模拟热钱包读取器
	mockHotAccountReader := NewMockHotAccountReader(t)
	pm.hotAccountReader = mockHotAccountReader
	mockHotAccountReader.On("GetHotAccount", mock.Anything, constant.EthChainIndex).Return("0x1234567890123456789012345678901234567890123456789012345678901234", nil)

	ctx := context.Background()
	senderAddr := common.HexToAddress("******************************************")
	gasAmount := decimal.NewFromInt(1000000000000000000) // 1 ETH

	// 执行转账，应该回退到默认gas limit
	txHash, err := pm.transferGasToSender(ctx, senderAddr, gasAmount)

	// 验证结果 - 即使EstimateGas失败，也应该成功执行
	assert.NoError(t, err)
	assert.NotEmpty(t, txHash)

	// 验证mock调用
	mockClient.AssertExpectations(t)
	mockHotAccountReader.AssertExpectations(t)
}
